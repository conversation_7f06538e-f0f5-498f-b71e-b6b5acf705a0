# 开发环境
.idea/
*.pyc
__pycache__/
*.env
.cursor

# 测试和临时文件
/test/
/release/
/my_content/

# 媒体文件
*.srt
*.mp4
*.exe
*.ass

# 输出和工作目录
/output/
/work-dir/

# 日志
logs/
*.log

# 虚拟环境
venv/
.venv/

# 系统文件
.DS_Store
Thumbs.db

# IDE 配置
.idea/
.vscode/
*.sublime-project
*.sublime-workspace

# 测试相关
tests/temp/
tests/.pytest_cache/
tests/data/temp/
.coverage
htmlcov/

# 文档
docs/_build/
docs/_static/
docs/_templates/
.python-version
